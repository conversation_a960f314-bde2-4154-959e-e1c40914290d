package br.com.pacto.swagger.respostas;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "DTO para representar a resposta da consulta de quantidade de créditos que um aluno pode transferir")
public class ConsultarQuantosCreditosOAlunoPodeTransferirResponseDTO {
    
    @ApiModelProperty(value = "Quantidade de créditos que o aluno pode transferir para outros alunos. Este valor representa o saldo disponível de créditos que podem ser transferidos do contrato especificado.",
                     example = "15")
    private Integer quantidadeDeCreditosPermitidos;

    public Integer getQuantidadeDeCreditosPermitidos() {
        return quantidadeDeCreditosPermitidos;
    }

    public void setQuantidadeDeCreditosPermitidos(Integer quantidadeDeCreditosPermitidos) {
        this.quantidadeDeCreditosPermitidos = quantidadeDeCreditosPermitidos;
    }
}
