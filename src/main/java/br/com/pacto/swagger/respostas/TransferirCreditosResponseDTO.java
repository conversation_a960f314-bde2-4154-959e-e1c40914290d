package br.com.pacto.swagger.respostas;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "DTO para representar a resposta da transferência de créditos entre clientes")
public class TransferirCreditosResponseDTO {
    
    @ApiModelProperty(value = "Resultado da operação de transferência de créditos. Contém a mensagem de confirmação da transferência realizada com sucesso ou informações sobre o processamento da operação.",
                     example = "Transferência de 5 créditos realizada com sucesso do contrato 12345 para o cliente João Silva")
    private String resultadoDaTransferencia;
    
    @ApiModelProperty(value = "Status da operação. Indica se a operação foi executada com sucesso ou se ocorreu algum erro durante o processamento.",
                     example = "success")
    private String status;
    
    @ApiModelProperty(value = "Mensagem adicional sobre a operação. Fornece detalhes complementares sobre o resultado da transferência ou informações sobre erros ocorridos.",
                     example = "Operação processada com sucesso")
    private String mensagem;

    public String getResultadoDaTransferencia() {
        return resultadoDaTransferencia;
    }

    public void setResultadoDaTransferencia(String resultadoDaTransferencia) {
        this.resultadoDaTransferencia = resultadoDaTransferencia;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getMensagem() {
        return mensagem;
    }

    public void setMensagem(String mensagem) {
        this.mensagem = mensagem;
    }
}
